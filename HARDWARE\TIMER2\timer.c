#include "timer.h"
#include "led.h"
#include "dac.h"
extern u8 Res;
int KAISHI=0;
//////////////////////////////////////////////////////////////////////////////////	 
//������ֻ��ѧϰʹ�ã�δ���������ɣ��������������κ���;
//ALIENTEK STM32F407������
//��ʱ�� ��������	   
//����ԭ��@ALIENTEK
//������̳:www.openedv.com
//��������:2014/5/4
//�汾��V1.0
//��Ȩ���У�����ؾ���
//Copyright(C) �������������ӿƼ����޹�˾ 2014-2024
//All rights reserved									  
////////////////////////////////////////////////////////////////////////////////// 	 


//ͨ�ö�ʱ��3�жϳ�ʼ��
//arr���Զ���װֵ��
//psc��ʱ��Ԥ��Ƶ��
//��ʱ�����ʱ����㷽��:Tout=((arr+1)*(psc+1))/Ft us.
//Ft=��ʱ������Ƶ��,��λ:Mhz
//����ʹ�õ��Ƕ�ʱ��3!

//void TIM1_PWM_Init(u32 arr2,u32 ccr2)
//{ 
//	
//	GPIO_InitTypeDef GPIO_InitStructure;
//	TIM_TimeBaseInitTypeDef  TIM_TimeBaseStructure;
//	TIM_OCInitTypeDef  TIM_OCInitStructure;
//  TIM_DeInit(TIM1);	

//	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM1, ENABLE);	//ʹ�ܶ�ʱ��1ʱ��
//	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);//ʹ��GPIOE��ʱ��
// 
//   //���ø�����Ϊ�����������,���TIM1 CH1��PWM���岨��	GPIOE.9
//	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8; //TIM1_CH1��ӦPE9
//  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
//	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
//	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
//	GPIO_InitStructure.GPIO_Speed = GPIO_Fast_Speed;
//	GPIO_Init(GPIOA, &GPIO_InitStructure);//��ʼ��GPIO
//  GPIO_PinAFConfig(GPIOA,GPIO_PinSource8,GPIO_AF_TIM1);
//   //��ʼ��TIM1      1s����һ��
//	TIM_TimeBaseStructure.TIM_Period = arr2; //��������һ�������¼�װ�����Զ���װ�ؼĴ������ڵ�ֵ
//	TIM_TimeBaseStructure.TIM_Prescaler =168-1; //����������ΪTIMxʱ��Ƶ�ʳ�����Ԥ��Ƶֵ 
//	TIM_TimeBaseStructure.TIM_ClockDivision =0; //����ʱ�ӷָ�:TDTS = Tck_tim
//	TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up; //���ϼ���ģʽ
//	//TIM_TimeBaseStructure.TIM_RepetitionCounter = TIM_CKD_DIV1;
//	TIM_TimeBaseInit(TIM1, &TIM_TimeBaseStructure); //����TIM_TimeBaseInitStruct��ָ���Ĳ�����ʼ��TIMx��ʱ�������λ
//	
//	//��ʼ��TIM1 Channel1 PWMģʽ2	 
//	TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM2; //ѡ��ʱ��ģʽ:TIM������ȵ���ģʽ2��С��CCRΪ��Ч��ƽ������CCRΪ��Ч��ƽ
//	TIM_OCInitStructure.TIM_OCIdleState = TIM_OCIdleState_Set;
// 	TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable; //�Ƚ����ʹ��
//	TIM_OCInitStructure.TIM_OCNIdleState = TIM_OCNIdleState_Reset;
//	TIM_OCInitStructure.TIM_OutputNState = TIM_OutputNState_Disable;
//	TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High; //�������:TIM����Ƚϼ��Ը�
//	TIM_OCInitStructure.TIM_Pulse =ccr2; //���ô�װ�벶��ȽϼĴ���������ֵ����������ֵ�ͻ�����ߵ�ƽ
//	TIM_OC1Init(TIM1, &TIM_OCInitStructure);  //����Tָ���Ĳ�����ʼ������TIM2 OC2

//	TIM_OC1PreloadConfig(TIM1, TIM_OCPreload_Disable);  //ʹ��TIM1��CCR1�ϵ�Ԥװ�ؼĴ���

//	TIM_Cmd(TIM1, ENABLE);
//  TIM_CtrlPWMOutputs(TIM1, ENABLE); 
//}





void TIM3_Int_Init(u16 arr,u16 psc)
{
	 TIM_TimeBaseInitTypeDef   TIM_TimeBaseInitstruct;
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3,ENABLE);

	TIM_TimeBaseInitstruct.TIM_Period=arr;
    TIM_TimeBaseInitstruct.TIM_Prescaler=psc;
	TIM_TimeBaseInitstruct.TIM_CounterMode=TIM_CounterMode_Up;
	TIM_TimeBaseInitstruct.TIM_ClockDivision=TIM_CKD_DIV1;
	TIM_TimeBaseInit(TIM3,&TIM_TimeBaseInitstruct);

	// 确保TIM3中断被禁用，只用作ADC触发源
	TIM_ITConfig(TIM3,TIM_IT_Update,DISABLE);
	TIM_SelectOutputTrigger(TIM3, TIM_TRGOSource_Update);
	//TIM_Cmd(TIM3,DISABLE);
    // TIM_Cmd(TIM3,ENABLE);

}

void TIM4_Int_Init(u16 arr1,u16 psc1)
{
	TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	
	 RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM4,ENABLE);  ///ʹ��TIM3ʱ��
	
    TIM_TimeBaseInitStructure.TIM_Period = arr1; 	//�Զ���װ��ֵ
	TIM_TimeBaseInitStructure.TIM_Prescaler=psc1;  //��ʱ����Ƶ
	TIM_TimeBaseInitStructure.TIM_CounterMode=TIM_CounterMode_Up; //���ϼ���ģʽ
	TIM_TimeBaseInitStructure.TIM_ClockDivision=TIM_CKD_DIV1; 
	
	TIM_TimeBaseInit(TIM4,&TIM_TimeBaseInitStructure);//��ʼ��TIM3
	
	TIM_ITConfig(TIM4,TIM_IT_Update,ENABLE); //������ʱ��3�����ж�
	TIM_Cmd(TIM4,ENABLE); //ʹ�ܶ�ʱ��3
	
	NVIC_InitStructure.NVIC_IRQChannel=TIM4_IRQn; //��ʱ��3�ж�
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=0x01; //��ռ���ȼ�1
	NVIC_InitStructure.NVIC_IRQChannelSubPriority=0x03; //�����ȼ�3
	NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;
	NVIC_Init(&NVIC_InitStructure);
	
}




// 定时器3中断服务函数 - 用于ADC触发，通常不需要中断处理
// TIM3主要用作ADC的外部触发源，通过TRGO信号触发ADC转换
// 如果需要监控TIM3状态，可以启用此中断处理函数
void TIM3_IRQHandler(void)
{
	if(TIM_GetITStatus(TIM3,TIM_IT_Update)==SET) // 溢出中断
	{
		// TIM3主要用于ADC触发，一般不需要在中断中处理
		// 如果需要调试TIM3工作状态，可以在这里添加代码

		// 清除中断标志位
		TIM_ClearITPendingBit(TIM3,TIM_IT_Update);
	}
}

//��ʱ��3�жϷ�����
void TIM4_IRQHandler(void)
{
	if(TIM_GetITStatus(TIM4,TIM_IT_Update)==SET) //����ж�
	{
		LED0=!LED0;//��ת
            if(Res==0x01)
        { 
           LED1=0; 
           KAISHI=1; 
         }
	}
	TIM_ClearITPendingBit(TIM4,TIM_IT_Update);  //����жϱ�־λ
}

// TIM6用于DAC DMA触发，800kHz触发频率
void TIM6_DAC_Init(u16 arr, u16 psc)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;

    // 使能TIM6时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM6, ENABLE);

    // 配置TIM6基本参数
    TIM_TimeBaseInitStructure.TIM_Period = arr;        // 自动重装载值
    TIM_TimeBaseInitStructure.TIM_Prescaler = psc;     // 预分频值
    TIM_TimeBaseInitStructure.TIM_CounterMode = TIM_CounterMode_Up;  // 向上计数
    TIM_TimeBaseInitStructure.TIM_ClockDivision = TIM_CKD_DIV1;

    TIM_TimeBaseInit(TIM6, &TIM_TimeBaseInitStructure);

    // 配置TIM6的TRGO输出，用于触发DAC
    TIM_SelectOutputTrigger(TIM6, TIM_TRGOSource_Update);

    // 启动TIM6
    TIM_Cmd(TIM6, ENABLE);
}

// TIM6中断服务函数 - 已改为DMA模式，不再使用中断
/*
void TIM6_DAC_IRQHandler(void)
{
    if (TIM_GetITStatus(TIM6, TIM_IT_Update) == SET)
    {
        // 更新DAC正弦波输出
        DAC_UpdateSineOutput();

        // 清除中断标志位
        TIM_ClearITPendingBit(TIM6, TIM_IT_Update);
    }
}
*/
